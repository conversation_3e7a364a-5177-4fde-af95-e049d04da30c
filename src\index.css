@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import 'aos/dist/aos.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* TinyMCE Mobile Responsive Styles */
@media (max-width: 768px) {
  /* Mobile TinyMCE Container */
  .tox-tinymce {
    border-radius: 8px !important;
    border: 2px solid #e5e7eb !important;
  }

  /* Mobile Toolbar */
  .tox-toolbar {
    flex-wrap: wrap !important;
    padding: 8px !important;
    gap: 4px !important;
  }

  .tox-toolbar__group {
    margin: 2px !important;
  }

  /* Mobile Toolbar Buttons */
  .tox-tbtn {
    width: 36px !important;
    height: 36px !important;
    margin: 1px !important;
    border-radius: 6px !important;
  }

  .tox-tbtn svg {
    width: 18px !important;
    height: 18px !important;
  }

  /* Mobile Editor Content Area */
  .tox-edit-area {
    border-radius: 0 0 8px 8px !important;
  }

  .tox-edit-area iframe {
    min-height: 300px !important;
  }

  /* Mobile Menubar (hidden) */
  .tox-menubar {
    display: none !important;
  }

  /* Mobile Status Bar */
  .tox-statusbar {
    padding: 8px 12px !important;
    font-size: 12px !important;
  }

  /* Mobile Dropdown Menus */
  .tox-collection {
    max-height: 200px !important;
    overflow-y: auto !important;
  }

  /* Mobile Dialog Boxes */
  .tox-dialog {
    max-width: 95vw !important;
    max-height: 90vh !important;
    margin: 5vh auto !important;
  }

  .tox-dialog__body {
    padding: 12px !important;
  }

  /* Mobile Form Fields in Dialogs */
  .tox-dialog .tox-textfield,
  .tox-dialog .tox-textarea {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 12px !important;
  }

  /* Mobile Quick Toolbar */
  .tox-pop {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-radius: 8px !important;
  }

  .tox-pop .tox-toolbar {
    padding: 6px !important;
  }

  /* Mobile Color Picker */
  .tox-swatches {
    grid-template-columns: repeat(6, 1fr) !important;
  }

  /* Mobile Table Tools */
  .tox-toolbar--scrolling {
    overflow-x: auto !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
  }

  .tox-toolbar--scrolling::-webkit-scrollbar {
    display: none !important;
  }
}

/* Tablet Responsive Styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .tox-toolbar {
    flex-wrap: wrap !important;
    padding: 10px !important;
  }

  .tox-tbtn {
    width: 32px !important;
    height: 32px !important;
    margin: 2px !important;
  }

  .tox-edit-area iframe {
    min-height: 400px !important;
  }
}

/* Touch-friendly improvements for all devices */
.tox-tbtn:hover,
.tox-tbtn:focus {
  background-color: #f3f4f6 !important;
  border-color: #f97316 !important;
}

.tox-tbtn--enabled {
  background-color: #fed7aa !important;
  border-color: #f97316 !important;
}

/* Custom scrollbar for content area */
.tox-edit-area iframe {
  scrollbar-width: thin !important;
  scrollbar-color: #f97316 #f3f4f6 !important;
}

/* Focus styles for accessibility */
.tox-tinymce:focus-within {
  border-color: #f97316 !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
}

/* Modern Design System Variables */
:root {
  /* Primary Colors */
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #8b5cf6;

  /* Secondary Colors */
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --accent-light: #22d3ee;

  /* Neutral Colors */
  --dark: #0f172a;
  --dark-light: #1e293b;
  --light: #f8fafc;
  --light-dark: #f1f5f9;
  --gray: #64748b;
  --gray-light: #94a3b8;

  /* Glass Morphism */
  --glass: rgba(255, 255, 255, 0.1);
  --glass-dark: rgba(0, 0, 0, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-display: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--dark);
  background: var(--light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode styles */
.dark body {
  color: #f8fafc;
  background: #0f172a;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--gray);
  border-radius: var(--radius-lg);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-light);
}

/* Glass Morphism Utility Classes */
.glass {
  background: var(--glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-dark {
  background: var(--glass-dark);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animation Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-gradient {
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

/* Keyframes */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Focus States */
.focus-ring {
  transition: box-shadow var(--transition-fast);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Post Content Styling */
.post-content {
  font-family: var(--font-primary);
  line-height: 1.8;
  color: #374151;
}

.post-content h1,
.post-content h2,
.post-content h3,
.post-content h4,
.post-content h5,
.post-content h6 {
  font-family: var(--font-display);
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #111827;
}

.post-content h1 {
  font-size: 2.25rem;
  line-height: 1.2;
}

.post-content h2 {
  font-size: 1.875rem;
  line-height: 1.3;
}

.post-content h3 {
  font-size: 1.5rem;
  line-height: 1.4;
}

.post-content p {
  margin-bottom: 1.5rem;
  font-size: 1.125rem;
  line-height: 1.8;
}

.post-content blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  font-size: 1.25rem;
  color: #6b7280;
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
}

.post-content code {
  background: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #dc2626;
}

.post-content pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 2rem 0;
}

.post-content pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

.post-content ul,
.post-content ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

.post-content li {
  margin-bottom: 0.5rem;
  line-height: 1.7;
}

.post-content a {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-color: rgba(99, 102, 241, 0.3);
  text-underline-offset: 0.25rem;
  transition: all var(--transition-fast);
}

.post-content a:hover {
  text-decoration-color: var(--primary);
  color: var(--primary-dark);
}

.post-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  margin: 2rem 0;
  box-shadow: var(--shadow-lg);
}

.post-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.post-content th,
.post-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.post-content th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive Design Improvements */
@media (max-width: 640px) {
  .post-content h1 {
    font-size: 1.875rem;
  }

  .post-content h2 {
    font-size: 1.5rem;
  }

  .post-content h3 {
    font-size: 1.25rem;
  }

  .post-content p {
    font-size: 1rem;
  }

  .post-content blockquote {
    padding: 1rem;
    font-size: 1.125rem;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-float,
  .animate-pulse-slow,
  .animate-fade-in,
  .animate-slide-up,
  .animate-gradient {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid #fff;
  }
}

/* Focus indicators */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .post-content {
    font-size: 12pt;
    line-height: 1.5;
  }

  .post-content h1,
  .post-content h2,
  .post-content h3 {
    page-break-after: avoid;
  }

  .post-content p {
    orphans: 3;
    widows: 3;
  }
}
