{"name": "WaveLink", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "setup": "node setup.js"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^1.9.7", "@tinymce/tinymce-react": "^5.1.1", "aos": "^2.3.4", "appwrite": "^13.0.2", "caniuse-lite": "^1.0.30001690", "framer-motion": "^12.23.0", "heroicons": "^2.2.0", "html-react-parser": "^4.2.10", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-redux": "^8.1.3", "react-router-dom": "^6.28.1", "remixicon": "^4.6.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.30", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}